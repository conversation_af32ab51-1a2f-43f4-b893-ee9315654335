<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\ProfessionalRegistrationProgress;

class UserCheckMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = auth()->user();

        // Allow non-authenticated users to proceed (for public routes like home)
        if (!auth()->check()) {
            return $next($request);
        }

        if (auth()->check()) {
            if (auth()->user()->hasRole('professional')) {
                $message = '';
                $title = '';
                $hasIssue = false;

                if ($user->approval == 0) {
                    $title = 'Account Not Approved';
                    $message = 'Your account is not yet approved. Please wait for approval.';
                    $hasIssue = true;
                }
                if ($user->status == 0) {
                    $title = 'Account Not Active';
                    $message = 'Your account is not active. Please contact support.';
                    $hasIssue = true;
                }
                if ($user->registration_completed == 0) {
                    return redirect()->route('register.user_type', 'professional');
                }

                // Only logout and redirect if there's an actual issue
                if ($hasIssue) {
                    auth()->logout();
                    session()->flush();
                    return redirect()->route('home')->with([
                        'title' => $title,
                        'message' => $message,
                        'type' => 'error',
                    ]);
                }
            }

            if (auth()->user()->hasRole('customer')) {
                $message = '';
                $title = '';
                $hasIssue = false;

                if ($user->registration_completed == 0) {
                    return redirect()->route('register.user_type', 'customer');
                }

                if ($user->status == 0) {
                    $title = 'Account Not Active';
                    $message = 'Your account is not active. Please contact support.';
                    $hasIssue = true;
                }

                // Only logout and redirect if there's an actual issue
                if ($hasIssue) {
                    auth()->logout();
                    session()->flush();
                    return redirect()->route('home')->with([
                        'title' => $title,
                        'message' => $message,
                        'type' => 'error',
                    ]);
                }
            }
        }
        return $next($request);
    }
}
