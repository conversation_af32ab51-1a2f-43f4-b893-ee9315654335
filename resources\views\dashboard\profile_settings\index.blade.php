@extends(auth()->check() && auth()->user()->hasRole('customer') ? 'website.layout.master' : 'dashboard.layout.master')
@push('js')
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
@endpush
@section('content')
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard profile-setting">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <div class="row">
                <div class="col-md-12 d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="sora black">Profile</h4>
                        <p class="fs-14 light-black sora">Lorem ipsum dolor sit amet consectetur. </p>
                    </div>

                    @if (auth()->check() && auth()->user()->hasRole('customer'))
                        <div>
                            <button class="drop-btn delete-btn btn btn-outline-danger  py-2 px-3 text-center"><i
                                    class="bi bi-trash p-0 red me-3"></i>Delete Profile
                            </button>
                        </div>
                    @endif
                </div>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <div class="card white-box friends-cards ">
                        <div class="card-header align-items-center justify-content-center flex-column gap-3 py-20">

                            <img src="{{ asset('website') . '/' . $user->profile->pic ?? '/images/image_input_holder.png' }}"
                                class="customer_profile" alt="card-image" />
                            <p class="fs-22 sora black semi_bold">{{ $user->name ?? '' }}</p>
                        </div>
                        <div class="card-body">
                            <p class="fs-12 normal sora light-black"><span
                                    class="me-3">@include('svg.building')</span>
                                {{ $user->email ?? '' }}</p>
                            @if ($user->profile->country)
                                <p class="fs-12 normal sora light-black"><span
                                        class="me-3">@include('svg.pin')</span>
                                    {{ $user->profile->city ?? '' }}, {{ $user->profile->country ?? '' }}</p>
                            @endif
                            <p class="fs-12 normal sora light-black"></p><span class="me-3"><i
                                    class="fa-regular fa-star"></i></span>No
                            reviews</p>

                            <div class="d-flex gap-4 mt-5">
                                <a href="https://www.facebook.com/" target="_blank" class="logo-box">
                                    @include('svg.fb-logo')</a>
                                <a href="https://www.instagram.com/" target="_blank" class="logo-box">
                                    @include('svg.insta-logo')</a>
                                <a href="https://www.x.com/" target="_blank" class="logo-box">
                                    @include('svg.x-logo')</a>
                                <a href="https://www.tiktok.com/" target="_blank" class="logo-box">
                                    @include('svg.tiktok-logo')</a>
                              
                            </div>

                              <button type="button" class="blue-button mt-8" data-bs-toggle="modal"
                                    data-bs-target="#edit-social-modal">Edit Profile</button>
                        </div>
                    </div>
                </div>
                <div class="col-md-8">
                    <div class="row row-gap-5">
                        @if (Auth::user()->hasAnyRole(['individual', 'business', 'professional']))
                            <!-- businesss and individual -->
                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Profile Images</p>
                                        <button type="button"
                                            class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0">Edit</button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            <div class="col-md-3 ">
                                                <img src="{{ asset('website') }}/assets/images/professional2.png"
                                                    class="h-100 w-100 rounded-3  object-fit-contain top-rated-image"
                                                    alt="card-image" />
                                            </div>
                                            <div class="col-md-3 ">
                                                <img src="{{ asset('website') }}/assets/images/card-image.png"
                                                    class="h-100 w-100 rounded-3  object-fit-contain top-rated-image"
                                                    alt="card-image" />
                                            </div>
                                            <div class="col-md-3 ">
                                                <img src="{{ asset('website') }}/assets/images/card-image.png"
                                                    class="h-100 w-100 rounded-3 object-fit-contain top-rated-image"
                                                    alt="card-image" />
                                            </div>
                                            <div class="col-md-3 ">
                                                <img src="{{ asset('website') }}/assets/images/card-image.png"
                                                    class="h-100 w-100 rounded-3  object-fit-contain top-rated-image"
                                                    alt="card-image" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Personal Info</p>
                                        <button type="button" class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0"
                                            data-bs-toggle="modal" data-bs-target="#personal-info-modal">Edit</button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            <div class="col-md-6 ">
                                                <label for="full-name" class="form-label form-input-labels">Full
                                                    name</label>
                                                <input type="text" class="form-control form-inputs-field"
                                                    placeholder="Enter full-name" id="full-name" name="full-name"
                                                    value="Roger Press">
                                            </div>
                                            <div class="col-md-6 ">
                                                <label for="email" class="form-label form-input-labels">Email
                                                    Address</label>
                                                <input type="email" class="form-control form-inputs-field"
                                                    placeholder="Enter email address" id="email" name="email"
                                                    value="<EMAIL>" disabled>
                                            </div>
                                            <div class="col-md-6 ">
                                                <label for="phone-number" class="form-label form-input-labels">Phone
                                                    Number</label>
                                                <input type="tel" class="form-control form-inputs-field"
                                                    placeholder="Enter phone number " id="phone-number"
                                                    name="phone-number" value="+56-955-588-939">
                                            </div>
                                            <div class="col-md-6 ">
                                                <label for="location"
                                                    class="form-label form-input-labels">Location</label>
                                                <input type="text" class="form-control form-inputs-field"
                                                    placeholder="Enter email address" id="location" name="location"
                                                    value="6391 Elgin St. Celina, Delaware 10299">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Company details</p>
                                        <button type="button"
                                            class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0">Edit</button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            <div class="col-md-12 ">
                                                <label for="company-name" class="form-label form-input-labels">Company
                                                    name</label>
                                                <input type="text" class="form-control form-inputs-field"
                                                    placeholder="Enter company name" id="company-name"
                                                    name="company-name" value="Acme - Acme Corporation">
                                            </div>
                                            <div class="col-md-6 ">
                                                <label for="company-id" class="form-label form-input-labels">Company
                                                    ID</label>
                                                <input type="email" class="form-control form-inputs-field"
                                                    placeholder="Enter company id" id="company-id" name="company-id"
                                                    value="ABC123">
                                            </div>
                                            <div class="col-md-6 ">
                                                <label for="company-vat-number"
                                                    class="form-label form-input-labels">Company VAT
                                                    number</label>
                                                <input type="tel" class="form-control form-inputs-field"
                                                    placeholder="Enter company vat number " id="company-vat-number"
                                                    name="company-vat-number" value="GB123456789">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Service details</p>
                                        <button type="button"
                                            class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0">Edit</button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            <div class="col-md-12">
                                                <p class="fs-14 black regular">Primary Services</p>
                                                <div class="d-flex gap-4 flex-wrap">
                                                    @for ($i = 0; $i < 6; $i++)
                                                        <p class="fs-14 sora light-black normal service-details">Makeup
                                                            Artists</p>
                                                    @endfor
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <p class="fs-14 black regular">Secondary Services</p>
                                                <div class="d-flex gap-4 flex-wrap">
                                                    @for ($i = 0; $i < 6; $i++)
                                                        <p class="fs-14 sora light-black normal service-details">Makeup
                                                            Artists</p>
                                                    @endfor
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Product Certifications</p>
                                        <button type="button"
                                            class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0">Edit</button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            <div class="col-md-12">
                                                <div class="d-flex gap-4 flex-wrap">
                                                    @for ($i = 0; $i < 8; $i++)
                                                        <p
                                                            class="fs-14 sora light-black normal service-details align-items-center">
                                                            <span>
                                                                <img src="{{ asset('website') }}/assets/images/certificate.png"
                                                                    class="h-25px w-25px object-fit-contain rounded-pill top-rated-image"
                                                                    alt="card-image" />
                                                            </span>
                                                            <span> Makeup Artists</span>
                                                        </p>
                                                    @endfor
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Certifications & Licenses</p>
                                        <button type="button"
                                            class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0">Edit</button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            @for ($i = 0; $i < 2; $i++)
                                                <div class="col-md-6">
                                                    <div
                                                        class="card card-box flex-row gap-3 justify-content-center align-items-center">
                                                        <div
                                                            class="card-header border-0 card-box justify-content-center align-items-center p-7">
                                                            <img src="{{ asset('website') }}/assets/images/licenses.png"
                                                                class="h-50px w-50px object-fit-contain top-rated-image"
                                                                alt="card-image" />
                                                        </div>
                                                        <div class="card-body p-0">
                                                            <p class="black sora fs-16 semi_bold m-0">Certified Stylist
                                                                Program</p>
                                                            <p class="black fs-14 normal m-0"> <span
                                                                    class="link-gray">Issued by:
                                                                </span>Lorem Ipsum</p>
                                                            <p class="black fs-14 normal m-0"> <span
                                                                    class="link-gray">Issue
                                                                    Date:</span> 08, 11, 2025</p>
                                                            <p class="black fs-14 normal m-0"> <span class="link-gray">End
                                                                    Date:
                                                                </span> 08, 11, 2025</p>

                                                        </div>
                                                    </div>
                                                </div>
                                            @endfor
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Availability</p>
                                        <button type="button"
                                            class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0">Edit</button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            <div class="col-md-12">
                                                <p class="fs-14 black regular">Weekly Availability </p>
                                                <div class="d-flex gap-2 flex-wrap">
                                                    @for ($i = 0; $i < 2; $i++)
                                                        <p class="fs-14 sora light-black normal service-details">Monday
                                                            (10:00am
                                                            - 7:00pm)</p>
                                                    @endfor
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <p class="fs-14 black regular">Holidays</p>
                                                <div class="d-flex gap-4 flex-wrap">
                                                    @for ($i = 0; $i < 6; $i++)
                                                        <p class="fs-14 sora light-black normal service-details">New Year's
                                                            Day
                                                            ( January 1)</p>
                                                    @endfor
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Intro Card</p>
                                        <button type="button"
                                            class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0">Add</button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            @for ($i = 0; $i < 3; $i++)
                                                <div class="col-md-6">
                                                    <div
                                                        class="card card-box p-0 flex-row justify-content-center align-items-center p-3 gap-5">
                                                        <div
                                                            class="card-header border-0 p-0 justify-content-center align-items-center">
                                                            <img src="{{ asset('website') }}/assets/images/badge.png"
                                                                class="h-35px w-35px  object-fit-contain top-rated-image"
                                                                alt="card-image" />
                                                        </div>
                                                        <div class="card-body p-0">
                                                            <p class="fs-16 sora w-700 m-0 dark-blue">100% Satisfaction
                                                                Guaranteed </p>
                                                            <p class="fs-14 sora normal  m-0 light-gray">Lorem ipsum dolor
                                                                sit
                                                                amet </p>
                                                        </div>
                                                        <div class="card-footer p-0 border-0">
                                                            <div class="dropdown">
                                                                <a class="drop-btn" type="button"
                                                                    id="dropdownMenuButton" data-bs-toggle="dropdown"
                                                                    aria-expanded="false">
                                                                    <i class="bi bi-three-dots-vertical"></i>
                                                                </a>
                                                                <ul class="dropdown-menu"
                                                                    aria-labelledby="dropdownMenuButton" style="">
                                                                    <li>
                                                                        <button
                                                                            class="dropdown-item complete fs-14 regular "
                                                                            type="button">
                                                                            <i
                                                                                class="bi bi-check-circle complete-icon"></i>
                                                                            Mark as Complete
                                                                        </button>
                                                                    </li>
                                                                    <li>
                                                                        <button class="dropdown-item cancel fs-14 regular"
                                                                            type="button">
                                                                            <i class="fa-solid fa-xmark cancel-icon"></i>
                                                                            Cancel
                                                                        </button>
                                                                    </li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div
                                                        class="card card-box p-0 flex-row justify-content-center align-items-center p-3 gap-5">
                                                        <div
                                                            class="card-header border-0 p-0 justify-content-center align-items-center">
                                                            <img src="{{ asset('website') }}/assets/images/like-stars.png"
                                                                class="h-35px w-35px  object-fit-contain"
                                                                alt="card-image" />
                                                        </div>
                                                        <div class="card-body p-0">
                                                            <p class="fs-16 sora w-700 m-0 dark-blue">100% Satisfaction
                                                                Guaranteed </p>
                                                            <p class="fs-14 sora normal  m-0 light-gray">Lorem ipsum dolor
                                                                sit
                                                                amet </p>
                                                        </div>
                                                        <div class="card-footer p-0 border-0">
                                                            <div class="dropdown">
                                                                <a class="drop-btn" type="button"
                                                                    id="dropdownMenuButton" data-bs-toggle="dropdown"
                                                                    aria-expanded="false">
                                                                    <i class="bi bi-three-dots-vertical"></i>
                                                                </a>
                                                                <ul class="dropdown-menu"
                                                                    aria-labelledby="dropdownMenuButton" style="">
                                                                    <li>
                                                                        <button
                                                                            class="dropdown-item complete fs-14 regular "
                                                                            type="button">
                                                                            <i
                                                                                class="bi bi-check-circle complete-icon"></i>
                                                                            Mark as Complete
                                                                        </button>
                                                                    </li>
                                                                    <li>
                                                                        <button class="dropdown-item cancel fs-14 regular"
                                                                            type="button">
                                                                            <i class="fa-solid fa-xmark cancel-icon"></i>
                                                                            Cancel
                                                                        </button>
                                                                    </li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            @endfor
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- end business -->
                        @elseif(auth()->check() && auth()->user()->hasRole('customer'))
                            <!-- customer -->
                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Personal Info</p>
                                        <button type="button"
                                            class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0"
                                            data-bs-toggle="modal" data-bs-target="#personal-info-modal">Edit</button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            <div class="col-md-6 ">
                                                <label for="customer-full-name" class="form-label form-input-labels">Full
                                                    name</label>
                                                <input type="text" class="form-control form-inputs-field"
                                                    placeholder="Enter full-name" id="customer-full-name"
                                                    name="customer-full-name" value="{{ $user->name ?? '' }}" disabled>
                                            </div>
                                            <div class="col-md-6 ">
                                                <label for="customer-email" class="form-label form-input-labels">Email
                                                </label>
                                                <input type="email" class="form-control form-inputs-field"
                                                    placeholder="Enter email address" id="customer-email"
                                                    name="customer-email" value="{{ $user->email ?? '' }}" disabled>
                                            </div>

                                            <div class="col-md-12 ">
                                                <label for="customer-location"
                                                    class="form-label form-input-labels">Location</label>
                                                <input type="text" class="form-control form-inputs-field"
                                                    placeholder="Enter your location" id="customer-location"
                                                    name="customer-location" value="{{ $user->profle->email ?? '' }}"
                                                    disabled>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Service Preferences</p>
                                        <button type="button"
                                            class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0"
                                            data-bs-toggle="modal"
                                            data-bs-target="#service-preferences-modal">Edit</button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            <div class="col-md-12">
                                                <div class="d-flex gap-4 flex-wrap">
                                                    @forelse (Auth::user()->service_preferences as $service)
                                                        <p class="fs-14 sora light-black normal service-details">
                                                            {{ $service->name }}
                                                        </p>
                                                    @empty
                                                        <p class="fs-14 sora light-black normal service-details">No service
                                                            preferences added</p>
                                                    @endforelse
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Hair Type</p>
                                        <button type="button"
                                            class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0 edit-body-spec-btn"
                                            data-type="hair">Edit</button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            <div class="col-md-12">
                                                @php
                                                    $hairSpec = Auth::user()->getBodySpecificationByType('hair');
                                                @endphp
                                                @if ($hairSpec)
                                                    <div class="card flex-row gap-4 border-0 shadow-none">
                                                        <div class="card-header p-0 border-0 align-items-start">
                                                            <img src="{{ $hairSpec->image ? asset('website' . '/' . $hairSpec->image) : asset('website/assets/images/wax.png') }}"
                                                                class="h-125px w-125px rounded-3 object-fit-contain"
                                                                alt="hair-type-image" />
                                                        </div>
                                                        <div class="card-body p-0">
                                                            <p class="fs-16 sora black semi_bold">{{ $hairSpec->name }}
                                                            </p>
                                                            {!! $hairSpec->description ?? '' !!}
                                                        </div>
                                                    </div>
                                                @else
                                                    <p class="fs-15 black normal text-center">Hair type not added yet</p>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Skin Type</p>
                                        <button type="button"
                                            class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0 edit-body-spec-btn"
                                            data-type="skin">Edit</button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            <div class="col-md-12">
                                                @php
                                                    $skinSpec = Auth::user()->getBodySpecificationByType('skin');
                                                @endphp
                                                @if ($skinSpec)
                                                    <div class="card flex-row gap-4 border-0 shadow-none">
                                                        <div class="card-header p-0 border-0 align-items-start">
                                                            <img src="{{ $skinSpec->image ? asset('website' . '/' . $skinSpec->image) : asset('website/assets/images/wax.png') }}"
                                                                class="h-125px w-125px rounded-3 object-fit-contain"
                                                                alt="skin-type-image" />
                                                        </div>
                                                        <div class="card-body p-0">
                                                            <p class="fs-16 sora black semi_bold">{{ $skinSpec->name }}
                                                            </p>
                                                            {!! $skinSpec->description ?? '' !!}
                                                        </div>
                                                    </div>
                                                @else
                                                    <p class="fs-15 black normal text-center">Skin type not added yet</p>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Body Type</p>
                                        <button type="button"
                                            class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0 edit-body-spec-btn"
                                            data-type="body">Edit</button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            <div class="col-md-12">
                                                @php
                                                    $bodySpec = Auth::user()->getBodySpecificationByType('body');
                                                @endphp
                                                @if ($bodySpec)
                                                    <div class="card flex-row gap-4 border-0 shadow-none">
                                                        <div class="card-header p-0 border-0 align-items-start">
                                                            <img src="{{ $bodySpec->image ? asset('website' . '/' . $bodySpec->image) : asset('website/assets/images/wax.png') }}"
                                                                class="h-125px w-125px rounded-3 object-fit-contain"
                                                                alt="body-type-image" />
                                                        </div>
                                                        <div class="card-body p-0">
                                                            <p class="fs-16 sora black semi_bold">{{ $bodySpec->name }}
                                                            </p>
                                                            {!! $bodySpec->description ?? '' !!}
                                                        </div>
                                                    </div>
                                                @else
                                                    <p class="fs-15 black normal text-center">Body type not added yet</p>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Allergies</p>
                                        <button type="button"
                                            class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0 edit-body-spec-btn"
                                            data-type="allergy">Edit</button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            <div class="col-md-12">
                                                @php
                                                    $allergySpec = Auth::user()->getBodySpecificationByType('allergy');
                                                @endphp
                                                @if ($allergySpec)
                                                    <div class="card flex-row gap-4 border-0 shadow-none">
                                                        <div class="card-header p-0 border-0 align-items-start">
                                                            <img src="{{ $allergySpec->image ? asset('website' . '/' . $allergySpec->image) : asset('website/assets/images/wax.png') }}"
                                                                class="h-125px w-125px rounded-3 object-fit-contain"
                                                                alt="body-type-image" />
                                                        </div>
                                                        <div class="card-body p-0">
                                                            <p class="fs-16 sora black semi_bold">{{ $allergySpec->name }}
                                                            </p>
                                                            {!! $allergySpec->description ?? '' !!}
                                                        </div>
                                                    </div>
                                                @else
                                                    <p class="fs-15 black normal text-center">Allergies not added yet</p>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!--  end customer -- -->
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>
    @include('dashboard.profile_settings.modal.edit-personal-info-modal')
    @include('dashboard.profile_settings.modal.body-specifications-modal')
    @include('dashboard.profile_settings.modal.service-preferences-modal')
    @include('dashboard.profile_settings.modal.edit-social-modal')
@endsection
@push('js')
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="https://cdn.ckeditor.com/ckeditor5/39.0.1/classic/ckeditor.js"></script>
    <script>
        $(document).ready(function() {
            $('.edit-body-spec-btn').on('click', function() {
                var type = $(this).data('type');
                $('#type').val(type);
                var title = type.charAt(0).toUpperCase() + type.slice(1) + ' Type';
                $('#body-modal-title').text(title);
                $('#body_name').val('');
                $('#description').val('');
                // Reset image input
                var imageInput = $('#body-specifications-modal .image-input[data-kt-image-input="true"]');
                imageInput.addClass('image-input-empty').removeClass('image-input-changed');
                imageInput.find('.image-input-wrapper').css('background-image', 'none');
                imageInput.find('[data-kt-image-input-action="remove"]').addClass('d-none');
                imageInput.find('[data-kt-image-input-action="cancel"]').addClass('d-none');
                $("#body-specifications-modal").modal('show');
                $.ajax({
                    url: '{{ route('body-specifications.get') }}',
                    type: 'GET',
                    data: {
                        type: type
                    },
                    success: function(response) {
                        if (response.success && response.data) {
                            var data = response.data;
                            $('#body_name').val(data.name);
                            // Set CKEditor content if it exists
                            if (window.bodySpecEditor) {
                                window.bodySpecEditor.setData(data.description || '');
                            } else {
                                $('#description').val(data.description || '');
                            }
                            if (data.image) {
                                var baseImageUrl = $('meta[name="asset-url"]').attr(
                                    'content') || '/website';
                                var imageUrl = baseImageUrl + '/' + data.image;
                                var wrapper = imageInput.find('.image-input-wrapper');
                                wrapper.css('background-image', 'url(' + imageUrl + ')');
                                imageInput.removeClass('image-input-empty').addClass(
                                    'image-input-changed');
                                imageInput.find('[data-kt-image-input-action="remove"]')
                                    .removeClass('d-none');
                                imageInput.find('[data-kt-image-input-action="cancel"]')
                                    .removeClass('d-none');
                                // Store original image for cancel functionality
                                imageInput.data('original-image', imageUrl);
                            } else {
                                // Clear original image data
                                imageInput.removeData('original-image');
                            }
                        }
                    },
                    error: function() {
                        // Modal is already shown, just keep it open with empty form
                        console.log('Error loading data, showing empty form');
                    }
                });
            });
        });
    </script>
    {{-- <script>
        // Handle image input functionality for body specifications modal
        $(document).ready(function() {
            // Image input change handler
            $('#body-specifications-modal input[type="file"]').on('change', function(e) {
                const file = e.target.files[0];
                const imageInput = $(this).closest('.image-input');
                const wrapper = imageInput.find('.image-input-wrapper');

                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(event) {
                        wrapper.css('background-image', 'url(' + event.target.result + ')');
                        imageInput.removeClass('image-input-empty').addClass('image-input-changed');
                        imageInput.find('[data-kt-image-input-action="remove"]').removeClass('d-none');
                        imageInput.find('[data-kt-image-input-action="cancel"]').removeClass('d-none');
                    };
                    reader.readAsDataURL(file);
                }
            });

            // Remove image handler
            $('#body-specifications-modal [data-kt-image-input-action="remove"]').on('click', function() {
                const imageInput = $(this).closest('.image-input');
                const wrapper = imageInput.find('.image-input-wrapper');
                const fileInput = imageInput.find('input[type="file"]');

                wrapper.css('background-image', 'none');
                imageInput.addClass('image-input-empty').removeClass('image-input-changed');
                fileInput.val('');
                $(this).addClass('d-none');
                imageInput.find('[data-kt-image-input-action="cancel"]').addClass('d-none');
            });

            // Cancel image handler
            $('#body-specifications-modal [data-kt-image-input-action="cancel"]').on('click', function() {
                const imageInput = $(this).closest('.image-input');
                const wrapper = imageInput.find('.image-input-wrapper');
                const fileInput = imageInput.find('input[type="file"]');

                // Reset to original state (empty or with existing image)
                if (imageInput.data('original-image')) {
                    wrapper.css('background-image', 'url(' + imageInput.data('original-image') + ')');
                    imageInput.removeClass('image-input-empty').addClass('image-input-changed');
                } else {
                    wrapper.css('background-image', 'none');
                    imageInput.addClass('image-input-empty').removeClass('image-input-changed');
                    $(this).addClass('d-none');
                    imageInput.find('[data-kt-image-input-action="remove"]').addClass('d-none');
                }
                fileInput.val('');
            });
        });
    </script> --}}
    <script>
        $(document).ready(function() {
            // Add custom validation method for CKEditor
            $.validator.addMethod("ckeditorRequired", function(value, element) {
                if (window.bodySpecEditor) {
                    var editorData = window.bodySpecEditor.getData();
                    return editorData.length > 0;
                }
                return value.length > 0;
            }, "Please enter description");

            $("#body-specifications-form").validate({
                rules: {
                    image: {
                        required: true
                    },
                    type: {
                        required: true
                    },
                    name: {
                        required: true
                    },
                    description: {
                        ckeditorRequired: true
                    },
                },
                messages: {
                    image: {
                        required: "Please upload an image"
                    },
                    type: {
                        required: "Please select type"
                    },
                    name: {
                        required: "Please enter name"
                    },
                    description: {
                        required: "Please enter description"
                    },
                },
                submitHandler: function(form) {
                    // Update textarea with CKEditor content before submitting
                    if (window.bodySpecEditor) {
                        const editorData = window.bodySpecEditor.getData();
                        $('#description').val(editorData);
                    }
                    form.submit();
                },
            });

            $('#service-preferences-form').validate({
                rules: {
                    'services[]': {
                        required: true
                    }
                },
                messages: {
                    'services[]': {
                        required: "Please select at least one service"
                    }
                },
                errorElement: 'div',
                errorClass: 'text-danger fw-bold',
                errorPlacement: function(error, element) {
                    if (element.attr('name') === 'services[]') {
                        error.insertAfter(element.next('.select2-container'));
                    } else {
                        error.insertAfter(element);
                    }
                },
                submitHandler: function(form) {
                    form.submit();
                }
            });
        });
    </script>
    <script>
        // Initialize CKEditor for body specifications description
        $(document).ready(function() {
            $('#body-specifications-modal').on('shown.bs.modal', function() {
                if (!window.bodySpecEditor) {
                    ClassicEditor
                        .create(document.querySelector('#description'), {
                            toolbar: ['heading', '|', 'bold', 'italic', 'link', 'bulletedList',
                                'numberedList', '|', 'outdent', 'indent', '|', 'blockQuote',
                                'insertTable', '|', 'undo', 'redo'
                            ]
                        })
                        .then(editor => {
                            window.bodySpecEditor = editor;
                        })
                        .catch(error => {
                            console.error('CKEditor initialization error:', error);
                        });
                }
            });

            // Clean up CKEditor when modal is hidden
            $('#body-specifications-modal').on('hidden.bs.modal', function() {
                if (window.bodySpecEditor) {
                    window.bodySpecEditor.destroy()
                        .then(() => {
                            window.bodySpecEditor = null;
                        })
                        .catch(error => {
                            console.error('CKEditor cleanup error:', error);
                        });
                }
            });

            // Update form validation to work with CKEditor
            $('#body-specifications-form').on('submit', function(e) {
                if (window.bodySpecEditor) {
                    // Get CKEditor content and set it to the textarea
                    const editorData = window.bodySpecEditor.getData();
                    $('#description').val(editorData);
                }
            });
        });
    </script>
@endpush
