<div>
    <form
        action="{{ isset($service) ? route('services.update', ['service' => $service->ids, 'type' => 'individual']) : route('services.store', ['type' => 'individual']) }}"
        method="POST" enctype="multipart/form-data" class="form-add-services">
        @csrf
        @isset($service)
            @method('PUT')
        @endisset

        <div class="row row-gap-5">
            {{-- Service Name --}}
            <div class="col-md-12">
                <label for="service-name" class="form-label form-input-labels">
                    Service Name
                </label>
                <input type="text" class="form-control form-inputs-field" placeholder="Enter Service name"
                    id="service-name" name="name" value="{{ old('name', $service->name ?? '') }}">
                @error('name')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
            </div>
            {{-- Service Name End --}}


            {{-- Category Name --}}
            <div class="col-md-6">
                <label for="category" class="form-label form-input-labels">Category</label>
                <select class="form-select form-select-field" data-control="select2" data-dropdown-css-class="w-619px"
                    data-close-on-select="false" data-placeholder="Select an option" id="category" name="category_id">
                    <option></option>
                    @forelse ($categories as $category)
                        <option value="{{ $category->ids }}"
                            {{ old('category_id', $service->category->ids ?? '') == $category->ids ? 'selected' : '' }}>
                            {{ $category->name }}</option>
                    @empty
                        <option value="">No categories found</option>
                    @endforelse
                </select>
                @error('category_id')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
            </div>
            {{-- Category Name End --}}


            {{-- Subcategory --}}
            <div class="col-md-6">
                <label for="subcategory" class="form-label form-input-labels">Sub
                    Category</label>
                <select class="form-select form-select-field" data-control="select2" data-dropdown-css-class="w-619px"
                    data-close-on-select="false" data-placeholder="Select an option" data-allow-clear="true"
                    id="subcategory" name="subcategory_id">
                    @forelse ($service->category->subcategories ?? [] as $subcategory)
                        <option value="{{ $subcategory->ids }}"
                            {{ old('subcategory_id', $service->subcategory->ids ?? '') == $subcategory->ids ? 'selected' : '' }}>
                            {{ $subcategory->name }}</option>
                    @empty
                        <option></option>
                    @endforelse
                </select>
                @error('subcategory_id')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
            </div>
            {{-- Subcategory End --}}


            @if (auth()->check() && auth()->user()->hasRole('business'))
                <div class="col-md-12">
                    <label for="staff-member" class="form-label form-input-labels">Assign Staff
                        Member</label>
                    <select class="form-select form-select-field" data-control="select2"
                        data-dropdown-css-class="w-619px" data-close-on-select="false"
                        data-placeholder="Select an option" data-allow-clear="true" multiple="multiple"
                        id="staff-member" name="staff-member">
                        <option></option>
                        <option value="1">Option 1</option>
                        <option value="2">Option 2</option>
                    </select>
                </div>
            @endif

            {{-- Availability --}}
            <div class="col-md-12">
                <x-service-availability-component :availabilities="$service->availabilities ?? []" />
            </div>
            {{-- Availability End --}}


            {{-- Service Duration --}}
            <div class="col-md-6">
                <label for="duration" class="form-label form-input-labels">Service Duration</label>
                <select name="duration" id="duration" class="form-control form-select-field">
                    <option value="">Select Service Duration</option>
                    <option value="15" {{ old('duration', $service->duration ?? '') == '15' ? 'selected' : '' }}>15
                        min
                    </option>
                    <option value="30" {{ old('duration', $service->duration ?? '') == '30' ? 'selected' : '' }}>30
                        min
                    </option>
                    <option value="45" {{ old('duration', $service->duration ?? '') == '45' ? 'selected' : '' }}>40
                        min
                    </option>
                    <option value="60" {{ old('duration', $service->duration ?? '') == '60' ? 'selected' : '' }}>60
                        min
                    </option>
                </select>
                {{-- <input type="number" class="form-control form-inputs-field"
                    placeholder="Enter Services Duration" id="duration" name="duration"> --}}
                <!-- <select class="form-select form-select-field" id="duration" name="duration"
                    data-control="select2" data-placeholder="Select">
                    <option></option>
                    <option value="30 min">30 min</option>
                    <option value="40 min">40 min</option>
                </select> -->
                @error('duration')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
            </div>
            {{-- Service Duration End --}}


            {{-- Price --}}
            <div class="col-md-3">
                <label for="price" class="form-label form-input-labels">
                    Price <span class="normal opacity-6 light-black">(Inclusive VAT)</span>
                </label>
                <input type="number" class="form-control form-inputs-field" placeholder="Enter price" id="price"
                    name="price" value="{{ old('price', $service->price ?? '') }}">
                @error('price')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
            </div>
            {{-- Price End --}}

            {{-- Additional Costs --}}
            <div class="col-md-3">
                <label for="tax" class="form-label form-input-labels">Additional Costs</label>
                <input type="number" class="form-control form-inputs-field" placeholder="Enter additional cost"
                    id="tax" name="additional_cost"
                    value="{{ old('additional_cost', $service->additional_cost ?? '') }}">
                @error('additional_cost')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
            </div>
            {{-- Additional Costs End --}}

            {{-- Required Items --}}
            <div class="col-md-12">
                <label for="required-items" class="form-label form-input-labels">Required
                    Items For Service</label>
                <input type="text" class="form-control form-inputs-field"
                    placeholder="Enter required items (separate with comma)" id="required-items"
                    name="required_items" value="{{ old('required_items', $service->required_items ?? '') }}">
                @error('required_items')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
            </div>
            {{-- Required Items End --}}

            {{-- Description --}}
            <div class="col-md-12">
                <label for="description" class="form-label form-input-labels">Description</label>
                <textarea class="form-control form-inputs-field form-textarea-field" id="description" name="description"
                    rows="4" placeholder="Enter Description here">{{ old('description', $service->description ?? '') }}</textarea>
                @error('description')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
            </div>
            {{-- Description End --}}

            {{-- Onsite & Customer Location --}}
            <div class="col-md-4 d-flex gap-4">
                <label class="styled-checkbox d-flex gap-3">
                    <input type="checkbox" name="is_onsite" value="1" id="onsite-secondary"
                        @checked(old('is_onsite', $service->is_onsite ?? false))>
                    <span class="fs-14 light-black normal">On-site</span>
                </label>

                <label class="styled-checkbox d-flex gap-3">
                    <input type="checkbox" name="is_customer_location" value="1" @checked(old('is_customer_location', $service->is_customer_location ?? false))
                        id="customer-location-secondary">
                    <span class="fs-14 light-black normal">Customer Location</span>
                </label>
            </div>
            {{-- Onsite & Customer Location End --}}

            <div class="row row-gap-5">
                {{-- Physical Location --}}
                <div class="col-md-12 form-hide-box" id="physical-location-field">
                    <label for="physical-location-secondary" class="form-label form-input-labels">Physical
                        Location</label>
                    <input type="text" class="form-control form-inputs-field form-textarea-field"
                        id="physical-location-secondary" name="physical_location"
                        value="{{ old('physical_location', $service->physical_location ?? '') }}"
                        placeholder="2715 Ash Dr. San Jose, South Dakota 83475">
                    @error('physical_location')
                        <p class="text-danger">
                            {{ $message }}
                        </p>
                    @enderror
                </div>
                {{-- Physical Location End --}}

                {{-- Radius, Travel Time, Additional Service Charges --}}
                <div class="col-md-4 form-hide-box" id="radius-field">
                    <label for="radius" class="form-label form-input-labels">Radius</label>
                    <input type="number" class="form-control form-inputs-field" placeholder="Enter radius"
                        value="{{ old('radius', $service->radius ?? '') }}" id="radius" name="radius">
                    @error('radius')
                        <p class="text-danger">
                            {{ $message }}
                        </p>
                    @enderror
                </div>

                <div class="col-md-4 form-hide-box" id="traveltime-field">
                    <label for="traveltime" class="form-label form-input-labels">Travel
                        Time</label>
                    <input type="number" class="form-control form-inputs-field"
                        value="{{ old('travel_time', $service->travel_time ?? '') }}" placeholder="Enter travel time"
                        id="traveltime" name="travel_time">
                    @error('travel_time')
                        <p class="text-danger">
                            {{ $message }}
                        </p>
                    @enderror
                </div>

                <div class="col-md-4 form-hide-box" id="servicecharges-field">
                    <label for="servicecharges" class="form-label form-input-labels">Additional
                        Service Charges</label>
                    <input type="number" class="form-control form-inputs-field"
                        placeholder="Enter additional service charges" id="servicecharges" name="service_charges"
                        value="{{ old('service_charges', $service->service_charges ?? '') }}">
                    @error('service_charges')
                        <p class="text-danger">
                            {{ $message }}
                        </p>
                    @enderror
                </div>
            </div>

            {{-- Thumbnail Image --}}
            <div class="col-md-4 ">
                <label for="thumbnail-secondary" class="form-label form-input-labels">Thumbnail
                    Image</label>
                <div class="position-relative ">
                    <div class="image-input {{ $service->image ?? null ? 'image-input-changed' : 'image-input-empty' }}"
                        data-kt-image-input="true">
                        <div class="image-input-wrapper"
                            style="background-image: url('{{ asset('website') . '/' . ($service->image ?? '') }}');">
                        </div>
                        <label
                            class="image-label flex-column gap-3 align-items-center justify-content-center btn-active-color-primary shadow"
                            data-kt-image-input-action="change" data-bs-toggle="tooltip" data-bs-dismiss="click"
                            title="Change avatar">
                            <i class="bi bi-upload upload-icon"></i>
                            <span>Upload Image</span>
                            <span>50x50 px</span>
                            <input type="file" name="thumbnail" accept=".png, .jpg, .jpeg" />
                        </label>
                        <span
                            class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                            data-kt-image-input-action="cancel" data-bs-toggle="tooltip" data-bs-dismiss="click"
                            title="Cancel avatar">
                            <i class="ki-outline ki-cross fs-3"></i>
                        </span>
                        <span
                            class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                            data-kt-image-input-action="remove" data-bs-toggle="tooltip" data-bs-dismiss="click"
                            title="Remove avatar">
                            <i class="ki-outline ki-cross fs-3"></i>
                        </span>
                    </div>
                </div>
                @error('thumbnail')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
            </div>
            {{-- Thumbnail Image End --}}
            <div class="">
                <button type="submit" class="add-btn">
                    {{ $btn_text ?? 'Add' }}
                </button>
            </div>
        </div>
    </form>
</div>
