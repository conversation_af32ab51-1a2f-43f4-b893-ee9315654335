<!-- Service Preferences Modal -->
<div class="modal fade card-details" id="service-preferences-modal" aria-labelledby="service-preferences-modal"
    aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
            <div class="modal-header p-5">
                <p class="modal-title fs-15 sora black semi_bold m-0" id="modal-title">Service Preferences</p>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="service-preferences-form" action="{{ route('service-preferences.update') }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="row row-gap-5">
                        <div class="col-md-12">
                            <label for="services" class="form-label form-input-labels">Select Services</label>
                            <select class="form-select form-select-solid" data-control="select2"
                                data-placeholder="Select services" multiple name="services[]" id="services">
                                @if (isset($services))
                                    @foreach ($services as $service)
                                        <option value="{{ $service->id }}"
                                            {{ in_array($service->id, auth()->user()->service_preferences->pluck('id')->toArray()) ? 'selected' : '' }}>
                                            {{ $service->name }}
                                        </option>
                                    @endforeach
                                @endif
                            </select>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0 pt-0">
                    <button type="button" class="trans-button" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="add-btn">Update</button>
                </div>
            </form>
        </div>
    </div>
</div>