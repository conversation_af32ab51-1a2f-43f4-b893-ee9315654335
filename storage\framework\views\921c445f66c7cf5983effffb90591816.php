<?php $__env->startSection('content'); ?>
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard add-service padding-block">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <div class="row row-gap-5">
                <div class="col-md-12 breadcrumbs">
                    <h6 class="sora black">Add Services</h6>
                    <p class="fs-14 sora light-black m-0">
                        <a class="text-dark" href="<?php echo e(route('services.index')); ?>">Services</a>
                        <span class="mx-3">
                            <i class="fa-solid fa-chevron-right right-arrow"></i>
                        </span>
                        Add Services
                    </p>
                </div>
                
                <?php echo $__env->make('dashboard.service.form', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('dashboard.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\git-file\anders\resources\views/dashboard/service/create.blade.php ENDPATH**/ ?>