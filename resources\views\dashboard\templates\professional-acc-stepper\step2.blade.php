<div class="container form-card">
    <div class="row">
        <div class="col-12 mb-5 px-0">
            <h2 class="fs-title">What services do you offer?</h2>
            <p>Choose your primary and up to 3 related category</p>
        </div>
    </div>

    <div class="row mb-8">
        <div class="col-md-12">
            @foreach ($categories->chunk(3) as $categoryChunk)
                <div class="row parent_services">
                    @foreach ($categoryChunk as $category)
                        <div class="col-md-4 mb-8 service_category" data-ctg="{{ $category->id }}">
                            <div class="services-section custom-radio-group">
                                <label class="custom-radio">
                                    {{-- <input type="checkbox" name="category[{{ $loop->parent->index * 3 + $loop->index }}][id]" value="{{ $category->id }}"> --}}
                                    <span class="radio-box">
                                        <img src="{{ asset('website') . '/' . $category->image ?? '' }}" alt="icon"   class="service-icon">
                                        <span class="label-text">{{ $category->name ?? '' }}</span>
                                    </span>
                                </label>
                            </div>
                        </div>
                    @endforeach
                    <div class="col-md-12 sub_categories">
                        <div class="card">
                            @foreach ($categoryChunk as $category)
                                @if ($category->subcategories->isNotEmpty())
                                    <div class="sub_category" id="sub_category_{{ $category->id }}"
                                        data-sub-ctg="{{ $category->id }}">
                                        <div class="custom-checkbox-group my-5">
                                            @foreach ($category->subcategories as $subcategory)
                                                <label class="custom-checkbox">
                                                    <input type="checkbox" name="subcategories[]"
                                                        value="{{ $subcategory->id }}"
                                                        @if (in_array($subcategory->id, old('subcategories', auth()->user()->subcategories->pluck('id')->toArray()))) checked @endif>
                                                    <span class="checkbox-label">{{ $subcategory->name ?? '' }}</span>
                                                </label>
                                            @endforeach
                                        </div>
                                    </div>
                                @else
                                    <div class="sub_category" id="sub_category_{{ $category->id }}"
                                        data-sub-ctg="{{ $category->id }}">
                                        <p class="m-0">No subcategories available</p>
                                    </div>
                                @endif
                            @endforeach
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    </div>
</div>
