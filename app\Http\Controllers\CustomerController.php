<?php

namespace App\Http\Controllers;

use App\Models\Service;
use App\Models\UserBodySpecification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class CustomerController extends Controller
{
    public function profileSetting()
    {
        $user = auth()->user();
        $services = Service::where('status', 1)->get();
        return view('dashboard.profile_settings.index', compact('user', 'services'));
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'type' => 'required|in:hair,skin,body,allergy',
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        if ($validator->fails()) {
            $errors = implode('<br>', $validator->errors()->all());
            return redirect()->back()->withInput()->with([
                'type' => 'error',
                'message' => $errors,
                'title' => 'Validation Errors',
            ]);
        }
        $user = auth()->user();
        try {
            DB::beginTransaction();
            $specification = UserBodySpecification::where('user_id', $user->id)->where('type', $request->type)->first();
            if (!$specification) {
                $specification = new UserBodySpecification();
                $specification->user_id = $user->id;
                $specification->type = $request->type;
            }
            if ($request->hasFile('image')) {
                if ($specification->image) {
                    $this->deleteImage($specification->image);
                }
                $specification->image = $this->storeImage('body-specifications', $request->file('image'));
            }
            $specification->name = $request->name;
            $specification->description = $request->description;
            $specification->save();
            DB::commit();
            return redirect()->back()->with([
                'type' => "success",
                'message' => $specification->type . ' type saved successfully',
                'title' => "Success"
            ]);
        } catch (\Throwable $th) {
            DB::rollback();
            return redirect()->back()->with(["type" => "error", "message" => $th->getMessage(), "title" => "Error"]);
        }
    }

    public function getBodySpecification(Request $request)
    {
        $request->validate([
            'type' => 'required|in:hair,skin,body,allergy',
        ]);

        $user = auth()->user();
        $specification = UserBodySpecification::where('user_id', $user->id)
            ->where('type', $request->type)
            ->first();

        return response()->json([
            'success' => true,
            'data' => $specification
        ]);
    }

    public function updatePersonalInfo(Request $request)
    {
        $user = auth()->user();
        try {
            DB::beginTransaction();
            if ($request->hasFile('image')) {
                if ($user->profile->pic) {
                    $this->deleteImage($user->profile->pic);
                }
                $user->profile->pic = $this->storeImage('profile-images', $request->file('image'));
            }
            $user->name = $request->name;
            $user->email = $request->email;
            $user->profile->city = $request->city;
            $user->profile->country = $request->country;
            $user->profile->location = $request->location;
            $user->profile->phone = $request->phone;
            $user->save();
            $user->profile->save();
            DB::commit();
            return redirect()->back()->with([
                'type' => "success",
                'message' => 'Personal info updated successfully',
                'title' => "Success"
            ]);
        } catch (\Throwable $th) {
            DB::rollback();
            return redirect()->back()->with(["type" => "error", "message" => $th->getMessage(), "title" => "Error"]);
        }
    }

    public function updateServicePreferences(Request $request)
    {
        $user = auth()->user();
        try {
            DB::beginTransaction();
            if ($request->has('services')) {
                $user->service_preferences()->sync($request->services);
            }
            DB::commit();
            return redirect()->back()->with([
                'type' => "success",
                'message' => 'Service preferences updated successfully',
                'title' => "Success"
            ]);
        } catch (\Throwable $th) {
            DB::rollback();
            return redirect()->back()->with(["type" => "error", "message" => $th->getMessage(), "title" => "Error"]);
        }
    }
}
