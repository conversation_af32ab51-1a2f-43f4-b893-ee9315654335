<div class="container form-card">
    <div class="row">
        <div class="col-12 mb-5 px-0">
            <h2 class="fs-title">What services do you offer?</h2>
            <p>Choose your primary and up to 3 related category</p>
        </div>
    </div>

    <div class="row mb-8">
        <div class="col-md-12">
            <?php $__currentLoopData = $categories->chunk(3); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $categoryChunk): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="row parent_services">
                    <?php $__currentLoopData = $categoryChunk; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="col-md-4 mb-8 service_category" data-ctg="<?php echo e($category->id); ?>">
                            <div class="services-section custom-radio-group">
                                <label class="custom-radio">
                                    
                                    <span class="radio-box">
                                        <img src="<?php echo e(asset('website') . '/' . $category->image ?? ''); ?>" alt="icon"   class="service-icon">
                                        <span class="label-text"><?php echo e($category->name ?? ''); ?></span>
                                    </span>
                                </label>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <div class="col-md-12 sub_categories">
                        <div class="card">
                            <?php $__currentLoopData = $categoryChunk; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php if($category->subcategories->isNotEmpty()): ?>
                                    <div class="sub_category" id="sub_category_<?php echo e($category->id); ?>"
                                        data-sub-ctg="<?php echo e($category->id); ?>">
                                        <div class="custom-checkbox-group my-5">
                                            <?php $__currentLoopData = $category->subcategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subcategory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <label class="custom-checkbox">
                                                    <input type="checkbox" name="subcategories[]"
                                                        value="<?php echo e($subcategory->id); ?>"
                                                        <?php if(in_array($subcategory->id, old('subcategories', auth()->user()->subcategories->pluck('id')->toArray()))): ?> checked <?php endif; ?>>
                                                    <span class="checkbox-label"><?php echo e($subcategory->name ?? ''); ?></span>
                                                </label>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                    </div>
                                <?php else: ?>
                                    <div class="sub_category" id="sub_category_<?php echo e($category->id); ?>"
                                        data-sub-ctg="<?php echo e($category->id); ?>">
                                        <p class="m-0">No subcategories available</p>
                                    </div>
                                <?php endif; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
</div>
<?php /**PATH D:\git-file\anders\resources\views/dashboard/templates/professional-acc-stepper/step2.blade.php ENDPATH**/ ?>