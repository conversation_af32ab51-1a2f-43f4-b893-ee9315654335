<div class="form-card frst-step">
    <div class="container">
        <div class="row">
            <div class="col-md-12 mb-10">
                <h2>Create a professional account</h2>
                <p>You're almost there! Create your new account for <?php echo e(auth()->user()->email); ?> by completing these details.</p>
            </div>

            <div class="col-md-12 mb-10">
                <div class="Image-input_holder mb-10">
                    <div class="image-input image-input-empty" data-kt-image-input="true">
                        <div class="image-input image-input-outline" data-kt-image-input="true">
                            <div class="image-input-wrapper w-125px h-125px"
                                style="background-image: url('<?php echo e(asset('website')); ?>/<?php echo e(auth()->user()->profile?->pic ?? ''); ?>'); background-size: cover; background-position: center;">
                            </div>
                            <label class="dark-green-btn fs-14 regular pt-9" data-kt-image-input-action="change">
                                <span class="pe-3 fs-16 fw-600 mb-10 blue-text"> Upload Logo</span>
                                <input type="file" name="avatar" class="no_validate" accept=".png, .jpg, .jpeg"   id="profileImage" />
                                <input type="hidden" name="avatar_remove" />
                                <p class="fs-24 medium pt-4 gray-text"> At least 500x500 px recommended. JPG or PNG is allowed</p>
                            </label>

                            <a href="#!" class="light-green-btn fs-14 regular ms-5"
                                data-kt-image-input-action="cancel" data-bs-toggle="tooltip" data-bs-dismiss="click"
                                title="Cancel avatar"> <i class="fas fa-times fa-5"></i> </a>

                            <a href="#!" class=" ms-5" data-kt-image-input-action="remove"><i   class="fas fa-trash-alt"></i> </a>
                        </div>
                    </div>
                    <p class="image-error-msg mt-5" style="color: red; display: none;">Image required</p>
                </div>
            </div>

            <div class="col-md-12">
                <label for="fullname" class="fieldlabels">Full Name</label>
                <input type="text" name="full_name" value="<?php echo e(old('full_name', auth()->user()->name)); ?>"  placeholder="Enter your first name" id="fullname" />
            </div>

            <div class="col-md-12">
                <label for="companyname" class="fieldlabels">Company Name</label>
                <input type="text" name="company_name"  value="<?php echo e(old('company_name', auth()->user()->profile?->company_name ?? '')); ?>" id="companyname"
                    placeholder="Enter your company name" />
            </div>

            <div class="col-md-12">
                <label for="email" class="fieldlabels">Email</label>
                <input type="email" name="email" value="<?php echo e(auth()->user()->email); ?>" id="email" readonly />
            </div>

            <div class="col-md-12">
                <label for="phone" class="fieldlabels">Phone</label>
                <input class="w-100 mb-6" type="tel"  value="<?php echo e(old('company_name', auth()->user()->profile?->phone ?? '')); ?>" id="phone"  name="phone">
            </div>

            <div class="col-md-12">
                <label for="website" class="fieldlabels">Website</label>
                <input type="url" name="website"  value="<?php echo e(old('website', auth()->user()->profile?->website ?? '')); ?>"  placeholder="Enter your website url" id="website" />
            </div>

            <div class="col-md-12">
                <label for="facebook" class="fieldlabels">Facebook</label>
                <input type="url" name="facebook"  value="<?php echo e(old('facebook', auth()->user()->profile?->facebook ?? '')); ?>"  placeholder="Enter your facebook url" id="facebook" />
            </div>

            <div class="col-md-12">
                <label for="instagram" class="fieldlabels">Instagram</label>
                <input type="url" name="instagram"
                    value="<?php echo e(old('instagram', auth()->user()->profile?->instagram ?? '')); ?>"
                    placeholder="Enter your Instagram url" id="instagram" />
            </div>

            <div class="col-md-12">
                <label for="tiktok" class="fieldlabels">TikTok</label>
                <input type="url" name="tiktok"
                    value="<?php echo e(old('tiktok', auth()->user()->profile?->tiktok ?? '')); ?>"
                    placeholder="Enter your TikTok url" id="tiktok" />
            </div>

            <div class="col-6">
                <label for="location" class="fieldlabels w-100">Location</label>
                <input type="text" name="location"
                    value="<?php echo e(old('location', auth()->user()->profile?->location ?? '')); ?>"
                    placeholder="Enter your location" id="location" />
            </div>

            <div class="col-6">
                <label for="location_service" class="fieldlabels ">Provide service within (km)</label>
                <input type="number" name="location_service"  value="<?php echo e(old('location_service', auth()->user()->profile?->location_service ?? '')); ?>"
                    placeholder="Enter your location" id="location_service" />
            </div>

            <div class="col-md-12">
                <label for="company_id" class="fieldlabels">Company ID</label>
                <input type="text" name="company_id"  value="<?php echo e(old('company_id', auth()->user()->profile?->company_id ?? '')); ?>"
                    placeholder="Enter company ID" id="company_id" />
            </div>

            <div class="col-md-12">
                <label for="vat_number" class="fieldlabels">Company VAT Number</label>
                <input type="text" name="vat_number"
                    value="<?php echo e(old('vat_number', auth()->user()->profile?->vat_number ?? '')); ?>"
                    placeholder="Enter company VAT number" id="vat_number" />
            </div>

            <div class="col-md-12">
                <div class="d-flex justify-content-between my-3 gap-8">
                    <a href="<?php echo e(route('google.calendar.connect')); ?>"
                        class="google_btn  w-md-50 w-sm-100 text-center"> <span class="pe-3"> <img
                                src="<?php echo e(asset('website')); ?>/assets/images/Google_Logo.svg"> </span> Connect Google
                        Calendar </a>

                    <a href="#!" class="outlook_btn  w-md-50 w-sm-100 text-center"> <span class="pe-3"> <img
                                src="<?php echo e(asset('website')); ?>/assets/images/outlook.svg"> </span> Connect Outlook
                        Calendar</a>
                </div>
            </div>
        </div>
    </div>
</div>
<?php /**PATH D:\git-file\anders\resources\views/dashboard/templates/professional-acc-stepper/step1.blade.php ENDPATH**/ ?>