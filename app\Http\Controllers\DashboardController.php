<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\CustomersExport;

class DashboardController extends Controller
{
    function index()
    {
        $data = [];
        if (auth()->user()->hasRole('admin')) {
            $data["totalCustomers"] = User::whereHas('roles', function ($q) {
                $q->where('name', 'customer');
            })->count();
            $data["totalProfessionals"] = User::whereHas('roles', function ($q) {
                $q->whereIn('name', ['individual', 'business']);
            })->count();
            $data["totalBookings"] = 0;
            $data["totalRevenue"] = 0;
            $data["professionals"] = User::whereHas('roles', function ($q) {
                $q->whereIn('name', ['individual', 'business']);
            })->get();
            $data["customers"] = User::whereHas('roles', function ($q) {
                $q->where('name', 'customer');
            })->get();
        }
        return view('dashboard.index', $data);
    }
    public function adminCustomers()
    {
        $customers = User::whereHas('roles', function ($q) {
            $q->where('name', 'customer');
        })->get();
        return view('dashboard.admin.customers', compact('customers'));
    }

    function changeStatus($id)
    {
        $user = User::whereHas('roles', function ($q) {
            $q->where('name', 'customer');
        })->where('ids', $id)->firstOrFail();

        if ($user->status == 1) {
            $user->status = 0;
            $message = 'Customer deactivated successfully';
        } else {
            $user->status = 1;
            $message = 'Customer activated successfully';
        }
        $user->save();
        return redirect()->back()->with(['title' => 'Done', 'message' => $message, 'type' => 'success']);
    }

    /**
     * Export customers to Excel
     */
    public function exportCustomers()
    {
        $customers = User::whereHas('roles', function ($q) {
            $q->where('name', 'customer');
        })->with(['profile'])->get();

        return Excel::download(new CustomersExport($customers), 'customers_' . date('Y-m-d') . '.xlsx');
    }
}
