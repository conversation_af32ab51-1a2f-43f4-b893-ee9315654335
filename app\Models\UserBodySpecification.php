<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserBodySpecification extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'type',
        'image',
        'name',
        'description',
        'be_aware',
        'recommendations',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
